@props([
    'name' => '',
    'value' => '',
    'placeholder' => 'Start typing...',
    'disabled' => false,
    'hasError' => false,
    'errorMessage' => '',
    'wireModel' => null
])

<div 
    x-data="richTextEditor({
        name: '{{ $name }}',
        value: @js($value),
        placeholder: '{{ $placeholder }}',
        disabled: {{ $disabled ? 'true' : 'false' }},
        wireModel: '{{ $wireModel }}'
    })"
    x-init="init(); initEditor()"
    x-on:destroy="destroy()"
    class="rich-text-editor border border-gray-300 rounded-lg overflow-hidden {{ $hasError ? 'border-red-500' : '' }}"
>
    <!-- Toolbar -->
    <div x-show="editor && !disabled" class="border-b border-gray-200 bg-gray-50 p-2">
        <div class="flex flex-wrap items-center gap-1">
            <!-- Bold -->
            <button
                type="button"
                @click="toggleBold()"
                :class="{ 'bg-gray-200': editor && editor.isActive('bold') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bold (Ctrl+B)"
            >
                <x-icons.lucide.bold class="w-4 h-4" />
            </button>

            <!-- Italic -->
            <button
                type="button"
                @click="toggleItalic()"
                :class="{ 'bg-gray-200': editor && editor.isActive('italic') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Italic (Ctrl+I)"
            >
                <x-icons.lucide.italic class="w-4 h-4" />
            </button>

            <!-- Underline -->
            <button
                type="button"
                @click="toggleUnderline()"
                :class="{ 'bg-gray-200': editor && editor.isActive('underline') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Underline (Ctrl+U)"
            >
                <x-icons.lucide.underline class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Bullet List -->
            <button
                type="button"
                @click="editor.chain().focus().toggleBulletList().run()"
                :class="{ 'bg-gray-200': editor.isActive('bulletList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Bullet List"
            >
                <x-icons.lucide.list class="w-4 h-4" />
            </button>

            <!-- Numbered List -->
            <button
                type="button"
                @click="editor.chain().focus().toggleOrderedList().run()"
                :class="{ 'bg-gray-200': editor.isActive('orderedList') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Numbered List"
            >
                <x-icons.lucide.list-ordered class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Link -->
            <button
                type="button"
                @click="toggleLink()"
                :class="{ 'bg-gray-200': editor.isActive('link') }"
                class="p-2 rounded hover:bg-gray-200 transition-colors"
                title="Add Link"
            >
                <x-icons.lucide.link class="w-4 h-4" />
            </button>

            <!-- Separator -->
            <div class="w-px h-6 bg-gray-300 mx-1"></div>

            <!-- Undo -->
            <button
                type="button"
                @click="editor.chain().focus().undo().run()"
                :disabled="!editor.can().undo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Undo (Ctrl+Z)"
            >
                <x-icons.lucide.undo class="w-4 h-4" />
            </button>

            <!-- Redo -->
            <button
                type="button"
                @click="editor.chain().focus().redo().run()"
                :disabled="!editor.can().redo()"
                class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Redo (Ctrl+Y)"
            >
                <x-icons.lucide.redo class="w-4 h-4" />
            </button>
        </div>
    </div>

    <!-- Editor Content -->
    <div
        x-ref="editorElement"
        class="min-h-[150px] p-1 text-sm/6 text-gray-700 focus-within:ring-0 overflow-y-auto max-h-[200px]"
    ></div>

    <!-- Hidden input for form submission -->
    @if($wireModel)
        <input type="hidden" wire:model.live="{{ $wireModel }}" x-model="content" />
    @else
        <input type="hidden" :name="name" x-model="content" />
    @endif

    <!-- Error Message -->
    @if($hasError && $errorMessage)
        <div class="text-red-500 text-sm mt-1 px-4 pb-2">
            {{ $errorMessage }}
        </div>
    @endif
</div>

<style>
/* Basic editor styling */
.rich-text-editor .ProseMirror {
    outline: none;
    min-height: 120px;
    padding: 0.5rem;
}

/* Placeholder styling */
.rich-text-editor .ProseMirror p.is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #9ca3af;
    pointer-events: none;
    height: 0;
}

/* Basic list styling */
.rich-text-editor .ProseMirror ul {
    list-style-type: disc;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror ol {
    list-style-type: decimal;
    margin-left: 1.5rem;
}

.rich-text-editor .ProseMirror li {
    margin: 0.25rem 0;
}
</style>
