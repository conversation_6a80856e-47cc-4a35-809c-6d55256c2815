import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'

// Alpine.js component for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: config.value || '',
        name: config.name || '',
        placeholder: config.placeholder || 'Start typing...',
        disabled: config.disabled || false,
        wireModel: config.wireModel || null,

        initEditor() {
            this.$nextTick(() => {
                this.editor = new Editor({
                    element: this.$refs.editorElement,
                    extensions: [
                        StarterKit,
                        Underline,
                        Link.configure({
                            openOnClick: false,
                            HTMLAttributes: {
                                class: 'text-blue-600 underline',
                            },
                        }),
                        Placeholder.configure({
                            placeholder: this.placeholder,
                        }),
                    ],
                    content: this.content,
                    editable: !this.disabled,
                    onUpdate: ({ editor }) => {
                        this.content = editor.getHTML();

                        // Trigger Alpine reactivity for Livewire
                        this.$nextTick(() => {
                            // This will trigger the x-model binding
                        });
                    },
                });

                // Watch for external content changes
                this.$watch('content', (newContent) => {
                    if (this.editor && this.editor.getHTML() !== newContent) {
                        this.editor.commands.setContent(newContent, false);
                    }
                });

                // Watch for disabled state changes
                this.$watch('disabled', (newDisabled) => {
                    if (this.editor) {
                        this.editor.setEditable(!newDisabled);
                    }
                });
            });
        },

        toggleLink() {
            if (this.editor.isActive('link')) {
                this.editor.chain().focus().unsetLink().run();
            } else {
                const url = window.prompt('Enter URL:');
                if (url) {
                    this.editor.chain().focus().setLink({ href: url }).run();
                }
            }
        },

        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        }
    }
}
