import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'

// Alpine.js component for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: config.value || '',
        name: config.name || '',
        placeholder: config.placeholder || 'Start typing...',
        disabled: config.disabled || false,
        wireModel: config.wireModel || null,

        init() {
            // Initialize content from Livewire if available
            if (this.wireModel && this.$wire && this.$wire.get(this.wireModel)) {
                this.content = this.$wire.get(this.wireModel);
            }
        },

        initEditor() {
            this.$nextTick(() => {
                if (this.$refs.editorElement) {
                    try {
                        this.editor = new Editor({
                            element: this.$refs.editorElement,
                            extensions: [
                                StarterKit,
                                Underline,
                                Link.configure({
                                    openOnClick: false,
                                    HTMLAttributes: {
                                        class: 'text-blue-600 underline',
                                    },
                                }),
                                Placeholder.configure({
                                    placeholder: this.placeholder,
                                }),
                            ],
                            content: this.content,
                            editable: !this.disabled,
                            onUpdate: ({ editor }) => {
                                const newContent = editor.getHTML();
                                if (this.content !== newContent) {
                                    this.content = newContent;

                                    // Trigger Livewire update if wireModel is set
                                    if (this.wireModel && this.$wire) {
                                        this.$wire.set(this.wireModel, this.content);
                                    }

                                    // Dispatch custom event for additional integrations
                                    this.$dispatch('rich-text-updated', { content: this.content });
                                }
                            },
                        });

                        // Watch for external content changes
                        this.$watch('content', (newContent) => {
                            if (this.editor && this.editor.getHTML() !== newContent) {
                                this.editor.commands.setContent(newContent, false);
                            }
                        });

                        // Watch for disabled state changes
                        this.$watch('disabled', (newDisabled) => {
                            if (this.editor) {
                                this.editor.setEditable(!newDisabled);
                            }
                        });
                    } catch (error) {
                        console.error('Failed to initialize TipTap editor:', error);
                    }
                }
            });
        },

        // Toolbar functions
        toggleBold() {
            if (this.editor) {
                this.editor.chain().focus().toggleBold().run();
            }
        },

        toggleItalic() {
            if (this.editor) {
                this.editor.chain().focus().toggleItalic().run();
            }
        },

        toggleUnderline() {
            if (this.editor) {
                this.editor.chain().focus().toggleUnderline().run();
            }
        },

        toggleLink() {
            if (this.editor.isActive('link')) {
                this.editor.chain().focus().unsetLink().run();
            } else {
                const url = window.prompt('Enter URL:');
                if (url) {
                    this.editor.chain().focus().setLink({ href: url }).run();
                }
            }
        },

        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        }
    }
}
